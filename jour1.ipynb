{"cells": [{"cell_type": "markdown", "id": "f303aaf9", "metadata": {}, "source": ["<h1>Partie definition<h1>"]}, {"cell_type": "code", "execution_count": null, "id": "718f029c", "metadata": {}, "outputs": [], "source": ["# Inisialisation de la connection\n", "import pyodbc\n", "conn = None  # Initialize conn to None\n", "def establish_connection():\n", "    # Build PyODBC connection string\n", "    connection_string = (\n", "        \"DRIVER={SQL Anywhere 17};\"\n", "        \"SERVER=gp40med;\"\n", "        \"HOST=*************:2638;\"\n", "        \"DATABASE=excalib;\"\n", "        \"UID=gpao;\"\n", "        \"PWD=flat;\"\n", "        \"CHARSET=UTF-8;\"\n", "    )\n", "    try:\n", "        # Establish the connection\n", "        print(\"Attempting to connect to SQL Anywhere via PyODBC...\")\n", "        conn = pyodbc.connect(connection_string)\n", "        print(\"Connection successful!\")\n", "        # Create a cursor object\n", "        cursor = conn.cursor()\n", "        # Example: Execute a simple query\n", "        print(\"Executing query: SELECT DB_NAME(), USER_ID();\")\n", "        cursor.execute(\"SELECT DB_NAME(), USER_ID();\")\n", "        # Fetch the results\n", "        row = cursor.fetchone()\n", "        if row:\n", "            print(f\"Connected to Database: {row[0]}, As User: {row[1]}\")\n", "        else:\n", "            print(\"Query executed, but no rows returned.\")\n", "        return conn\n", "    except pyodbc.Error as e:\n", "        print(f\"PyODBC Error: {e}\")\n", "        return None\n", "\n", "    except Exception as e:\n", "        print(f\"An unexpected error occurred: {e}\")\n", "        return None\n", "def close_connection(conn):\n", "\n", "    # Close the connection\n", "    if conn:\n", "        print(\"Closing connection.\")\n", "        conn.close()\n"]}, {"cell_type": "code", "execution_count": 56, "id": "d6866eda", "metadata": {}, "outputs": [], "source": ["def select(label,qte=None):\n", "    conn=establish_connection()\n", "    cursor = conn.cursor()\n", "    try:\n", "        if (qte) :\n", "            sql_query = f\"\"\"\n", "                SELECT top {qte} * FROM \"gpao\".{label};\n", "            \"\"\"\n", "        else :\n", "            sql_query = f\"\"\"\n", "                SELECT * FROM \"gpao\".{label};\n", "            \"\"\"\n", "        cursor.execute(sql_query)\n", "        print(\"\\nfirst 2 rows from ED_TRANSFERTS:\")\n", "        res=cursor.fetchall()\n", "        for i, r in enumerate(res):\n", "            print(f\"Row {i}: {r}\")\n", "\n", "    except pyodbc.E<PERSON>r as table_err:\n", "        print(f\"Error querying AFFAIRES: {table_err}\")\n", "    close_connection(conn)\n", "def exec_sql(sql_query):\n", "    conn=establish_connection()\n", "    cursor = conn.cursor()\n", "    try:\n", "        cursor.execute(sql_query)\n", "        print(f\"\\nresult of '{sql_query}'\")\n", "        res=cursor.fetchall()\n", "        print(res)\n", "    except pyodbc.E<PERSON>r as table_err:\n", "        print(f\"Error querying AFFAIRES: {table_err}\")\n", "    close_connection(conn)\n"]}, {"cell_type": "markdown", "id": "d5654655", "metadata": {}, "source": ["<h1>Partie Code<h1>"]}, {"cell_type": "code", "execution_count": 62, "id": "6ed1a105", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Attempting to connect to SQL Anywhere...\n", "Connection successful!\n", "Executing query: SELECT DB_NAME(), USER_ID();\n", "Connected to Database: excalib, As User: 101\n", "['NUMERO_OFDA', '<PERSON><PERSON><PERSON><PERSON>_OFDA', 'FERME_PREVISIONNEL', 'NUMERO_SERIE', 'CATE<PERSON><PERSON><PERSON>', 'PRODUIT', 'DESIGNATION', 'FAMILLE_TECHNIQUE', 'FAMILLE_VENTE', 'REFERENCE_VENTE', 'C<PERSON>IENT', 'NUMERO_SPECIFICATION', 'NUMERO_PLAN', 'CERTIFICAT_MATIERE', 'INDICE_PLAN', 'INDICE_GAMME', 'BESOIN_BRUT', 'BESOIN_NET', 'QUANTITE_DEMANDEE', 'UNITE', 'DISPO_DEMANDEE', 'LANCEMENT_AU_PLUS_TARD', 'LANCE_LE', 'DUREE_PREVUE', 'CUMUL_TEMPS_PASSES', 'CUMUL_ENTREES', 'CUMUL_NON_CONFORMITES', 'DER<PERSON>ER_PRIX_ACHAT', 'DER<PERSON>ER_PRIX_ACHAT_EU', 'FOURNISSEUR', 'NUMERO_MOUVEMENT_STK', 'DATE_MOUVEMENT_STK', 'MAGASIN', 'ALLEE', 'TRAVEE', 'NIVEAU', 'STATUT', 'DATE_EDITION', 'MOIS_LANCEMENT', 'COMMANDE', 'ID_POSTE', 'AFFAIRE', 'CMDE_CLIENT', 'CUMUL_DESTOCKAGE', 'QTE_MANUTENTION', 'FABRICANT', 'REF_FABRICANT', 'UNITE_FABRICATION', 'TYPE_ORDRE', 'TRACABILITE_COMPO', 'COMMENTAIRES', 'BLOQUE', 'INDICE_SPECIFICATION', 'ADR_LIVRAISON', 'VERSION_NOMENCLATURE', 'INDICE_NOMENCLATURE', 'VERSION_GAMME', 'DISPO_DEMANDEE_NO', 'LANCEMENT_AU_PLUS_TARD_NO', 'ORDONNANCE', 'DEMANDEUR', 'FERME_VERROUILLE', 'SITE_EMETTEUR', 'COEFF_TPS_GAMME', 'FAI', 'RANGEE', 'PROFONDEUR', 'STATUT_VERROUILLE', 'DELAI_OBTENTION']\n", "number of labels = 69\n", "Closing connection.\n"]}], "source": ["#List the OF_DA Labels\n", "conn=establish_connection()\n", "cursor = conn.cursor()\n", "try:\n", "    cursor.execute('SELECT * FROM \"gpao\".\"OF_DA\"')\n", "    res=cursor.fetchall()\n", "    columns = [column[0] for column in cursor.description]\n", "    print(columns)\n", "    print(f\"number of labels = {len(columns)}\")\n", "except pyodbc.E<PERSON>r as table_err:\n", "    print(f\"Error querying AFFAIRES: {table_err}\")\n", "close_connection(conn)\n"]}, {"cell_type": "code", "execution_count": 65, "id": "78313318", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Attempting to connect to SQL Anywhere...\n", "Connection successful!\n", "Executing query: SELECT DB_NAME(), USER_ID();\n", "Connected to Database: excalib, As User: 101\n", "\n", "first 2 rows from ED_TRANSFERTS:\n", "Row 0: ('A181000RE', None, 'F', '1800RE', 'A', '001E', 'MULTIMETRE DMM141', '0000', '0000', '001E', None, None, None, 0, '', '', 0.0, 0.0, 1.0, 'UNITE', '2018-10-12 00:00:00.000', '2018-10-12 00:00:00.000', '2018-10-12 00:00:00.000', 0.0, 0.0, 0.0, 0.0, 0.0, 971.5, 'E011', None, None, '0000', '', '', '', 'A', None, '2018/10', 'A201800068', '001', None, None, 0.0, 0.0, None, None, 1.0, 'DA', 0, None, 0, None, 'E000', None, '', None, '2018-10-12 00:00:00.000', '2018-10-12 00:00:00.000', 0, 'E015', 0, 'E000', 1.0, 0, '', '', 0, 0)\n", "Row 1: ('A181000RF', None, 'F', '1800RF', 'A', '002E', 'MULTIMETRE DMM121', '0000', '0000', '002E', None, None, None, 0, '', '', 0.0, 0.0, 1.0, 'UNITE', '2018-10-12 00:00:00.000', '2018-10-12 00:00:00.000', '2018-10-12 00:00:00.000', 0.0, 0.0, 0.0, 0.0, 0.0, 620.6, 'E011', None, None, '0000', '', '', '', 'A', None, '2018/10', 'A201800068', '002', None, None, 0.0, 0.0, None, None, 1.0, 'DA', 0, None, 0, None, 'E000', None, '', None, '2018-10-12 00:00:00.000', '2018-10-12 00:00:00.000', 0, 'E015', 0, 'E000', 1.0, 0, '', '', 0, 0)\n", "Closing connection.\n"]}], "source": ["select(\"OF_DA\",2)"]}, {"cell_type": "code", "execution_count": 63, "id": "86d77e64", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Attempting to connect to SQL Anywhere...\n", "Connection successful!\n", "Executing query: SELECT DB_NAME(), USER_ID();\n", "Connected to Database: excalib, As User: 101\n", "\n", "result of 'SELECT OF_DA.NUMERO_OFDA, OF_DA.PRODUIT, OF_DA.STATUT, OF_DA.LANCEMENT_AU_PLUS_TARD, OF_DA.QUANTITE_DEMANDEE, OF_DA.CUMUL_ENTREES, OF_DA.DUREE_PREVUE, OF_DA.CUMUL_TEMPS_PASSES FROM gpao.OF_DA OF_DA WHERE (OF_DA.NUMERO_OFDA Like 'F%') AND (OF_DA.STATUT Like 'C%')'\n", "[('F190300AH', 'VEG00032', 'C', '2019-05-02 00:00:00.000', 3060.0, 3056.0, 0.0, 1341.2231111113), ('F190300AI', 'VEG00043', 'C', '2019-05-02 00:00:00.000', 1020.0, 1012.0, 0.0, 491.3037777778), ('F190400BE', 'SOM00001', 'C', '2019-04-01 00:00:00.000', 120.0, 104.0, 308.4, 130.7424999998), ('F190900XG', 'SOM00021', 'C', '2019-10-17 00:00:00.000', 15.0, 14.0, 0.0, 15.465), ('F1911010Y', 'SOM00021', 'C', '2019-11-14 00:00:00.000', 100.0, 0.0, 0.0, 24.3290555556), ('F19110125', 'VEG00232', 'C', '2019-11-21 00:00:00.000', 12462.0, 11926.0, 0.0, 4368.909166667), ('F200400AY', 'VEG00232', 'C', '2020-05-28 00:00:00.000', 12364.0, 11900.0, 0.0, 3842.8954999997), ('F200400B0', 'VEG00055', 'C', '2020-06-29 00:00:00.000', 1035.0, 926.0, 0.0, 827.8157777779), ('F200400C4', 'VEG00243', 'C', '2020-05-18 00:00:00.000', 2070.0, 1924.0, 0.0, 1308.5273333331), ('F2201006B', 'ECO00086S', 'C', '2021-11-16 00:00:00.000', 1811.0, 1213.0, 842.8394, 313.4323888887), ('F220300C3', 'ECO00083S', 'C', '2022-04-27 00:00:00.000', 988.0, 630.0, 92.3884649948, 92.4508333333), ('F2210017J', 'PEDA0001', 'C', '2022-10-25 00:00:00.000', 400.0, 0.0, 228.0, 157.8305555556), ('F2210018H', 'ET00032', 'C', '2022-12-30 00:00:00.000', 1000.0, 0.0, 0.0, 601.5413888886), ('F2301002Z', 'ECO00129S', 'C', '2023-05-24 00:00:00.000', 1122.0, 702.0, 51.2754, 9.4397222222), ('F230100PL', 'MER00264', 'C', '2024-04-12 00:00:00.000', 1404.0, 1404.0, 78.624, 76.5886666666), ('F230100U4', 'ECO00081S', 'C', '2023-12-15 00:00:00.000', 360.0, 355.0, 154.116, 74.7597777776), ('F230100VJ', 'ECO00134S', 'C', '2024-05-17 00:00:00.000', 504.0, 256.0, 34.776, 30.7574999999), ('F230100VK', 'ECO00527', 'C', '2024-04-24 00:00:00.000', 300.0, 247.0, 46.2, 20.3836666667), ('F2401006S', 'NEX00988', 'C', '2024-04-18 00:00:00.000', 852.0, 815.0, 0.0, 36.2888333333), ('F2401007J', 'ECO00116S', 'C', '2024-05-27 00:00:00.000', 102.0, 100.0, 28.713, 24.4746111112), ('F240400DK', 'ECO00218S', 'C', '2024-07-01 00:00:00.000', 1002.0, 472.0, 150.7008, 49.3333333334), ('F240400ER', 'CRO00083', 'C', '2024-09-18 00:00:00.000', 400.0, 395.0, 53.6, 112.8523333335), ('F240400HZ', 'ECO00575', 'C', '2024-04-30 00:00:00.000', 1008.0, 252.0, 94.248, 85.3563333332), ('F240400JE', 'NEX00607', 'C', '2024-09-05 00:00:00.000', 1008.0, 968.0, 86.688, 103.6482222223), ('F240400JQ', 'NEX01207', 'C', '2024-08-19 00:00:00.000', 2502.0, 2465.0, 227.682, 117.3649999999), ('F240400JW', 'NEX01215', 'C', '2024-09-02 00:00:00.000', 1008.0, 271.0, 105.84, 39.3005555556), ('F240400JY', 'NEX01214', 'C', '2024-08-19 00:00:00.000', 1008.0, 0.0, 74.592, 27.8783333333), ('F240400K8', 'NEX01242', 'C', '2024-08-28 00:00:00.000', 1008.0, 612.0, 89.712, 139.8296666668), ('F240400K9', 'NEX01249', 'C', '2024-08-28 00:00:00.000', 1008.0, 729.0, 89.712, 49.0477777778), ('F240400LW', 'OTI00918', 'C', '2024-11-08 00:00:00.000', 50.0, 0.0, 8.74125874, 11.9158333333), ('F240400M4', 'VEG02450', 'C', '2024-07-25 00:00:00.000', 2054.0, 1000.0, 292.4896, 586.3039999999), ('F240400M5', 'STAR00001', 'C', '2024-09-12 00:00:00.000', 2000.0, 0.0, 89.2, 451.8956666666), ('F240400MJ', 'TLV00056S', 'C', '2024-09-26 00:00:00.000', 200.0, 197.0, 9.52, 24.0568333333), ('F240400MY', 'TLV00057S', 'C', '2025-09-23 00:00:00.000', 200.0, 197.0, 28.48, 18.2041666666), ('F240400SQ', 'NEX01059', 'C', '2024-12-10 00:00:00.000', 207.0, 175.0, 16.353, 8.8333333333), ('F240400SZ', 'NEX01233', 'C', '2024-11-19 00:00:00.000', 504.0, 504.0, 29.736, 67.0435000001), ('F240400T0', 'NEX01234', 'C', '2024-11-25 00:00:00.000', 504.0, 0.0, 44.856, 90.7911111111), ('F240400T1', 'NEX01235', 'C', '2024-12-03 00:00:00.000', 504.0, 249.0, 63.504, 39.0216666667), ('F240400UT', 'VEG02440', 'C', '2024-12-29 00:00:00.000', 2323.0, 1620.0, 0.0, 692.8807222221), ('F240400V8', 'ECO00119S', 'C', '2025-02-04 00:00:00.000', 702.0, 348.0, 54.054, 71.0266666665), ('F240400V9', 'ECO00295', 'C', '2024-12-20 00:00:00.000', 1200.0, 600.0, 558.10374672, 133.7345555557), ('F240400VJ', 'ECO00551', 'C', '2025-03-19 00:00:00.000', 720.0, 564.0, 90.0, 75.8991666667), ('F240400VK', 'ECO00561', 'C', '2025-01-21 00:00:00.000', 1600.0, 1600.0, 262.4, 267.6066111111), ('F240400VO', 'CRO00003', 'C', '2025-01-20 00:00:00.000', 400.0, 0.0, 35.96, 60.5281111112), ('F25010005', 'ECO00525', 'C', '2025-04-18 00:00:00.000', 102.0, 48.0, 35.496, 8.23), ('F2501000Q', 'MKM00001', 'C', '2024-10-30 00:00:00.000', 1000.0, 1000.0, 0.0, 0.0), ('F2501000R', 'MKM00002', 'C', '2024-10-30 00:00:00.000', 500.0, 500.0, 0.0, 1.9830555555), ('F2501000S', 'MKM00003', 'C', '2024-10-30 00:00:00.000', 1800.0, 1800.0, 0.0, 5.7911111111), ('F2502003D', 'NEX01404', 'C', '2025-03-27 00:00:00.000', 102.0, 102.0, 6.12, 0.0), ('F2502003N', 'NEX01403', 'C', '2025-03-05 00:00:00.000', 102.0, 0.0, 8.16, 13.7944444444), ('F2502003O', 'NEX01402', 'C', '2025-03-05 00:00:00.000', 102.0, 0.0, 6.12, 25.863), ('F25020044', 'NEX01270', 'C', '2025-02-19 00:00:00.000', 5004.0, 5004.0, 330.264, 429.4284444447), ('F2503004O', 'STO00032', 'C', '2025-01-20 00:00:00.000', 1020.0, 0.0, 358.632, 325.6556111112), ('F2503005M', 'OTI01146', 'C', '2025-05-21 00:00:00.000', 27.0, 26.0, 15.39, 14.1408333333), ('F2503005Q', 'EVO00451', 'C', '2025-04-08 00:00:00.000', 228.0, 90.0, 103.74, 175.9615555555), ('F25030060', 'NEX01424', 'C', '2025-04-08 00:00:00.000', 1000.0, 401.0, 55.5, 60.775388889), ('F25030061', 'NEX01425', 'C', '2025-04-08 00:00:00.000', 1000.0, 0.0, 37.0, 52.0991666667), ('F25030066', 'EVO00350', 'C', '2025-04-18 00:00:00.000', 500.0, 0.0, 32.55, 17.8569444444), ('F25030077', '12676S', 'C', '2025-04-29 00:00:00.000', 1.0, 0.0, 0.0, 545.1611666665), ('F2503007D', 'NEX00607', 'C', '2025-04-24 00:00:00.000', 999.0, 0.0, 85.914, 0.0), ('F2503007E', 'NEX00540', 'C', '2025-03-13 00:00:00.000', 999.0, 0.0, 60.939, 59.5041111112), ('F2503007F', 'NEX00539', 'C', '2025-03-25 00:00:00.000', 999.0, 0.0, 172.827, 16.5919444445), ('F2503007N', 'NEX01202', 'C', '2025-04-16 00:00:00.000', 1512.0, 1402.0, 134.568, 142.9466666666), ('F2503007O', 'NEX01199', 'C', '2025-03-21 00:00:00.000', 1512.0, 0.0, 137.592, 146.0530555555), ('F2503007V', 'STO00043', 'C', '2025-04-18 00:00:00.000', 1500.0, 0.0, 0.0, 240.0661666669), ('F25030083', 'OTI00759S', 'C', '2025-05-26 00:00:00.000', 180.0, 0.0, 10.134, 0.0), ('F2503008U', 'JCD02743', 'C', '2025-05-19 00:00:00.000', 30.0, 3.0, 189.9, 2.5038888889), ('F2503009O', 'EVO00352', 'C', '2025-05-23 00:00:00.000', 1900.0, 1900.0, 34.77, 25.2897222222), ('F2503009Q', 'NEX01270', 'C', '2025-04-02 00:00:00.000', 5022.0, 4886.0, 331.452, 233.1238888889), ('F2503009R', 'NEX00929', 'C', '2025-01-13 00:00:00.000', 5022.0, 0.0, 80.352, 132.7407777778), ('F2503009S', 'NEX00930', 'C', '2025-01-28 00:00:00.000', 5022.0, 0.0, 366.606, 208.0624999999), ('F2503009U', 'NEX00929', 'C', '2025-01-27 00:00:00.000', 5013.0, 0.0, 80.208, 0.0), ('F250300A8', 'CRO00003', 'C', '2025-05-23 00:00:00.000', 400.0, 0.0, 35.96, 10.0447222222), ('F250300AC', 'MAQ01523', 'C', '2025-05-29 00:00:00.000', 46.0, 46.0, 6.072, 2.0011111111), ('F250300AO', 'HUT00018', 'C', '2025-05-21 00:00:00.000', 20.0, 20.0, 3.92, 7.5386111111), ('F250300AP', 'HUT00021', 'C', '2025-05-21 00:00:00.000', 20.0, 20.0, 3.92, 1.0258333333)]\n", "Closing connection.\n"]}], "source": ["exec_sql(\"SELECT OF_DA.NUMERO_OFDA, OF_DA.PRODUIT, OF_DA.STATUT, OF_DA.LANCEMENT_AU_PLUS_TARD, OF_DA.QUANTITE_DEMANDEE, OF_DA.CUMUL_ENTREES, OF_DA.DUREE_PREVUE, OF_DA.CUMUL_TEMPS_PASSES FROM gpao.OF_DA OF_DA WHERE (OF_DA.NUMERO_OFDA Like 'F%') AND (OF_DA.STATUT Like 'C%')\")"]}, {"cell_type": "markdown", "id": "84dc9dcb", "metadata": {}, "source": ["\"\"\"SELECT OF_DA.NUMERO_OFDA, OF_DA.PRODUIT, OF_DA.STATUT, OF_DA.LANCEMENT_AU_PLUS_TARD, OF_DA.QUANTITE_DEMANDEE, OF_DA.CUMUL_ENTREES, OF_DA.DUREE_PREVUE, OF_DA.CUMUL_TEMPS_PASSES\n", "FROM gpao.OF_DA OF_DA\n", "WHERE (OF_DA.NUMERO_OFDA Like 'F%') AND (OF_DA.STATUT Like 'C%')\"\"\"\n", "\"\"\"SELECT HISTO_OF_DA.PRODUIT, HISTO_OF_DA.CATE<PERSON><PERSON><PERSON>, HISTO_OF_DA.DUREE_PREVUE, HISTO_OF_DA.CUMUL_TEMPS_PASSES, HISTO_OF_DA.CUMUL_ENTREES\n", "FROM gpao.HISTO_OF_DA HISTO_OF_DA\n", "ORDER BY HISTO_OF_DA.PRODUIT\"\"\"\n", "\"\"\"SELECT SALARIES.NOM, SALARIES.PRENOM, SALARIES.QUALIFICATION, SALARIES.ACTIF\n", "FROM gpao.SALARIES SALARIES\"\"\"\n", "\"\"\"SELECT OF_DA.NUMERO_OFDA, OF_DA.PRODUIT,OF_DA.LANCEMENT_AU_PLUS_TARD, OF_DA.LANCEMENT_AU_PLUS_TARD, OF_DA.QUANTITE_DEMANDEE, OF_DA.STATUT, OF_DA.CUMUL_ENTREES, OF_DA.DUREE_PREVUE, OF_DA.AFFAIRE, OF_DA.CUMUL_TEMPS_PASSES\n", "FROM gpao.OF_DA OF_DA\n", "WHERE (OF_DA.NUMERO_OFDA Like 'F%')\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "1e66e966", "metadata": {}, "outputs": [], "source": ["# List of labels (columns) for each table used in the application\n", "\n", "# Table: gpao.OF_DA (Main production orders table)\n", "of_da_columns = [\n", "    'NUMERO_OFDA',        # Order number\n", "    'PRODUIT',            # Product reference\n", "    'STATUT',             # Status (C=In progress, T=Completed, A=Stopped)\n", "    'LANCEMENT_AU_PLUS_TARD', # Latest launch date\n", "    'QUANTITE_DEMANDEE',  # Requested quantity\n", "    'CUMUL_ENTREES',      # Cumulative entries (produced quantity)\n", "    'DUREE_PREVUE',       # Planned duration\n", "    'CUMUL_TEMPS_PASSES', # Cumulative time spent\n", "    'AFFAIRE',            # Business/Order reference\n", "    'DESIGNATION',        # Description\n", "    'CATEGORIE',          # Category\n", "    'CLIENT'              # Client\n", "]\n", "\n", "# Table: gpao.SALARIES (Employees table)\n", "salaries_columns = [\n", "    'NOM',                # Last name\n", "    'PRENOM',             # First name\n", "    'QUALIFICATION',      # Qualification/Skill\n", "    'ACTIF',              # Active status\n", "    'NOM_COMPLET'         # Full name (calculated)\n", "]\n", "\n", "# Table: gpao.HISTO_OF_DA (Production orders history)\n", "histo_of_da_columns = [\n", "    'PRODUIT',            # Product reference\n", "    'CATEGORIE',          # Category\n", "    'DUREE_PREVUE',       # Planned duration\n", "    'CUMUL_TEMPS_PASSES', # Cumulative time spent\n", "    'CUMUL_ENTREES'       # Cumulative entries (produced quantity)\n", "]\n", "\n", "# Calculated fields in SQL queries\n", "calculated_fields = [\n", "    'SEMAINE',            # Week number (calculated from LANCEMENT_AU_PLUS_TARD)\n", "    'Avancement_PROD',    # Production progress (CUMUL_ENTREES / QUANTITE_DEMANDEE)\n", "    'Avancement_temps',   # Time progress (CUMUL_TEMPS_PASSES / DUREE_PREVUE)\n", "    'Alerte_temps',       # Time alert (1 if CUMUL_TEMPS_PASSES > DUREE_PREVUE, else 0)\n", "    'QUANTITE_RESTANTE',  # Remaining quantity (QUANTITE_DEMANDEE - CUMUL_ENTREES)\n", "    'EFFICACITE',         # Efficiency (DUREE_PREVUE / CUMUL_TEMPS_PASSES)\n", "    'COEFFICIENT_EFFICACITE', # Efficiency coefficient (based on qualification)\n", "    'SECTEUR',            # Sector (derived from QUALIFICATION)\n", "    'RETARD_JOURS',       # Delay in days\n", "    'PRIORITE',           # Priority (based on delay)\n", "    'TEMPS_RESTANT_ESTIME' # Estimated remaining time\n", "]\n", "\n", "# Print the lists to verify\n", "print(\"OF_DA Table Columns:\", len(of_da_columns))\n", "print(\"SALARIES Table Columns:\", len(salaries_columns))\n", "print(\"HISTO_OF_DA Table Columns:\", len(histo_of_da_columns))\n", "print(\"Calculated Fields:\", len(calculated_fields))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}