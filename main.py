#!/usr/bin/env python3
"""
FastAPI Application for Production Time Tracking - Excalibur ERP
Refactored from Streamlit to FastAPI for better scalability and hosting flexibility.
"""

from fastapi import FastAPI, Request, HTTPException, Depends, Query
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse, JSONResponse
from typing import Optional, Dict, Any, List
from datetime import datetime, date, timedelta
import pandas as pd
import json
import os
from pathlib import Path

# Import our data analyzer
from analyse_donnees import ExcaliburDataAnalyzer

# Create FastAPI app
app = FastAPI(
    title="Suivi Production - Excalibur ERP",
    description="API pour le suivi en temps réel des indicateurs de production",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Setup static files and templates
static_dir = Path("static")
templates_dir = Path("templates")

# Create directories if they don't exist
static_dir.mkdir(exist_ok=True)
templates_dir.mkdir(exist_ok=True)

app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# Global analyzer instance (will be initialized on startup)
analyzer: Optional[ExcaliburDataAnalyzer] = None

@app.on_event("startup")
async def startup_event():
    """Initialize the data analyzer on startup."""
    global analyzer
    try:
        analyzer = ExcaliburDataAnalyzer()
        print("✅ Data analyzer initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize data analyzer: {e}")
        analyzer = None

def get_analyzer() -> ExcaliburDataAnalyzer:
    """Dependency to get the analyzer instance."""
    if analyzer is None:
        raise HTTPException(
            status_code=503, 
            detail="Database connection not available. Please check configuration."
        )
    return analyzer

# Utility functions
def format_dataframe_for_json(df: pd.DataFrame) -> List[Dict[str, Any]]:
    """Convert DataFrame to JSON-serializable format."""
    if df is None or df.empty:
        return []
    
    # Convert datetime columns to strings
    for col in df.select_dtypes(include=['datetime64[ns]', 'datetimetz']).columns:
        df[col] = df[col].dt.strftime('%Y-%m-%d %H:%M:%S')
    
    # Convert to dict and handle NaN values
    return df.fillna('').to_dict('records')

def calculate_kpis(df: pd.DataFrame) -> Dict[str, Any]:
    """Calculate KPIs from the main OF data."""
    if df is None or df.empty:
        return {
            "total_of": 0,
            "of_en_cours": 0,
            "of_termines": 0,
            "of_arretes": 0,
            "avg_prod": 0,
            "avg_temps": 0,
            "alertes": 0,
            "efficacite": 0
        }
    
    try:
        # Convert to numeric if needed
        df['Avancement_PROD'] = pd.to_numeric(df['Avancement_PROD'], errors='coerce')
        df['Avancement_temps'] = pd.to_numeric(df['Avancement_temps'], errors='coerce')
        df['EFFICACITE'] = pd.to_numeric(df['EFFICACITE'], errors='coerce')
        
        return {
            "total_of": len(df),
            "of_en_cours": len(df[df['STATUT'] == 'C']),
            "of_termines": len(df[df['STATUT'] == 'T']),
            "of_arretes": len(df[df['STATUT'] == 'A']),
            "avg_prod": float(df['Avancement_PROD'].mean() * 100) if not df['Avancement_PROD'].isna().all() else 0,
            "avg_temps": float(df['Avancement_temps'].mean() * 100) if not df['Avancement_temps'].isna().all() else 0,
            "alertes": int(df['Alerte_temps'].sum()) if 'Alerte_temps' in df.columns else 0,
            "efficacite": float(df[(df['EFFICACITE'] > 0) & (df['EFFICACITE'] < 5)]['EFFICACITE'].mean()) if 'EFFICACITE' in df.columns else 0
        }
    except Exception as e:
        print(f"Error calculating KPIs: {e}")
        return {
            "total_of": len(df),
            "of_en_cours": 0,
            "of_termines": 0,
            "of_arretes": 0,
            "avg_prod": 0,
            "avg_temps": 0,
            "alertes": 0,
            "efficacite": 0
        }

# Routes

@app.get("/", response_class=HTMLResponse)
async def dashboard(request: Request):
    """Main dashboard page."""
    return templates.TemplateResponse("dashboard.html", {"request": request})

@app.get("/api/dashboard-data")
async def get_dashboard_data(
    date_debut: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    date_fin: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    statut_filter: Optional[str] = Query(None, description="Status filter (C/T/A)"),
    analyzer: ExcaliburDataAnalyzer = Depends(get_analyzer)
):
    """Get all dashboard data including KPIs and detailed information."""
    try:
        # Set default dates if not provided
        if not date_debut:
            date_debut = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        if not date_fin:
            date_fin = datetime.now().strftime('%Y-%m-%d')
        
        # Get all data
        dashboard_data = analyzer.get_dashboard_data(date_debut, date_fin, statut_filter)
        
        main_of_data = dashboard_data.get('main_of_data')
        charge_data = dashboard_data.get('charge_data')
        backlog_data = dashboard_data.get('backlog_data')
        personnel_data = dashboard_data.get('personnel_data')
        
        # Calculate KPIs
        kpis = calculate_kpis(main_of_data)
        
        return {
            "success": True,
            "kpis": kpis,
            "data": {
                "main_of": format_dataframe_for_json(main_of_data),
                "charge": format_dataframe_for_json(charge_data),
                "backlog": format_dataframe_for_json(backlog_data),
                "personnel": format_dataframe_for_json(personnel_data)
            },
            "filters": {
                "date_debut": date_debut,
                "date_fin": date_fin,
                "statut_filter": statut_filter
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching dashboard data: {str(e)}")

@app.get("/api/of-data")
async def get_of_data(
    date_debut: Optional[str] = Query(None),
    date_fin: Optional[str] = Query(None),
    statut_filter: Optional[str] = Query(None),
    famille_filter: Optional[str] = Query(None),
    client_filter: Optional[str] = Query(None),
    alerte_filter: Optional[bool] = Query(None),
    analyzer: ExcaliburDataAnalyzer = Depends(get_analyzer)
):
    """Get filtered OF data."""
    try:
        # Set default dates if not provided
        if not date_debut:
            date_debut = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        if not date_fin:
            date_fin = datetime.now().strftime('%Y-%m-%d')
        
        # Get main OF data
        df = analyzer.get_comprehensive_of_data(date_debut, date_fin, statut_filter)
        
        if df is not None and not df.empty:
            # Apply additional filters
            if famille_filter and famille_filter != "Toutes":
                df = df[df['FAMILLE_TECHNIQUE'] == famille_filter]
            if client_filter and client_filter != "Tous":
                df = df[df['CLIENT'] == client_filter]
            if alerte_filter is not None:
                df = df[df['Alerte_temps'] == alerte_filter]
        
        return {
            "success": True,
            "data": format_dataframe_for_json(df),
            "count": len(df) if df is not None else 0
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching OF data: {str(e)}")

@app.get("/api/report")
async def get_report(
    date_debut: Optional[str] = Query(None),
    date_fin: Optional[str] = Query(None),
    statut_filter: Optional[str] = Query(None),
    analyzer: ExcaliburDataAnalyzer = Depends(get_analyzer)
):
    """Generate and return a text report."""
    try:
        # Set default dates if not provided
        if not date_debut:
            date_debut = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        if not date_fin:
            date_fin = datetime.now().strftime('%Y-%m-%d')
        
        # Get main OF data
        df = analyzer.get_comprehensive_of_data(date_debut, date_fin, statut_filter)
        
        if df is None or df.empty:
            return {
                "success": True,
                "report": "Aucune donnée disponible pour générer le rapport."
            }
        
        # Generate report
        report_text = analyzer.generate_summary_report(df)
        
        return {
            "success": True,
            "report": report_text
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating report: {str(e)}")

@app.get("/api/export/csv")
async def export_csv(
    date_debut: Optional[str] = Query(None),
    date_fin: Optional[str] = Query(None),
    statut_filter: Optional[str] = Query(None),
    analyzer: ExcaliburDataAnalyzer = Depends(get_analyzer)
):
    """Export OF data as CSV."""
    from fastapi.responses import StreamingResponse
    import io

    try:
        # Set default dates if not provided
        if not date_debut:
            date_debut = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        if not date_fin:
            date_fin = datetime.now().strftime('%Y-%m-%d')

        # Get main OF data
        df = analyzer.get_comprehensive_of_data(date_debut, date_fin, statut_filter)

        if df is None or df.empty:
            raise HTTPException(status_code=404, detail="No data found for export")

        # Convert to CSV
        output = io.StringIO()
        df.to_csv(output, index=False, encoding='utf-8')
        output.seek(0)

        # Create filename
        filename = f"export_of_{date_debut}_to_{date_fin}.csv"

        return StreamingResponse(
            io.BytesIO(output.getvalue().encode('utf-8')),
            media_type="text/csv",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error exporting data: {str(e)}")

@app.get("/api/filters/options")
async def get_filter_options(
    analyzer: ExcaliburDataAnalyzer = Depends(get_analyzer)
):
    """Get available filter options for dropdowns."""
    try:
        # Get a sample of data to extract filter options
        date_debut = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        date_fin = datetime.now().strftime('%Y-%m-%d')

        df = analyzer.get_comprehensive_of_data(date_debut, date_fin, None)

        if df is None or df.empty:
            return {
                "success": True,
                "options": {
                    "familles": [],
                    "clients": [],
                    "statuts": ["C", "T", "A"]
                }
            }

        # Extract unique values for filters
        familles = sorted(df['FAMILLE_TECHNIQUE'].dropna().unique().tolist())
        clients = sorted(df['CLIENT'].dropna().unique().tolist())

        return {
            "success": True,
            "options": {
                "familles": familles,
                "clients": clients,
                "statuts": ["C", "T", "A"]
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching filter options: {str(e)}")

@app.get("/api/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "database_connected": analyzer is not None
    }

@app.get("/api/stats/summary")
async def get_summary_stats(
    date_debut: Optional[str] = Query(None),
    date_fin: Optional[str] = Query(None),
    analyzer: ExcaliburDataAnalyzer = Depends(get_analyzer)
):
    """Get summary statistics for the dashboard."""
    try:
        # Set default dates if not provided
        if not date_debut:
            date_debut = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        if not date_fin:
            date_fin = datetime.now().strftime('%Y-%m-%d')

        # Get all data
        dashboard_data = analyzer.get_dashboard_data(date_debut, date_fin, None)

        main_of_data = dashboard_data.get('main_of_data')
        charge_data = dashboard_data.get('charge_data')
        backlog_data = dashboard_data.get('backlog_data')
        personnel_data = dashboard_data.get('personnel_data')

        # Calculate summary statistics
        summary = {
            "of_stats": {
                "total": len(main_of_data) if main_of_data is not None else 0,
                "en_cours": len(main_of_data[main_of_data['STATUT'] == 'C']) if main_of_data is not None else 0,
                "termines": len(main_of_data[main_of_data['STATUT'] == 'T']) if main_of_data is not None else 0,
                "arretes": len(main_of_data[main_of_data['STATUT'] == 'A']) if main_of_data is not None else 0
            },
            "charge_stats": {
                "total_secteurs": len(charge_data) if charge_data is not None else 0,
                "total_operateurs": int(charge_data['NB_OPERATEURS'].sum()) if charge_data is not None else 0,
                "heures_disponibles": int(charge_data['NB_HEURES_DISPONIBLES_SEMAINE'].sum()) if charge_data is not None else 0
            },
            "backlog_stats": {
                "total_of": len(backlog_data) if backlog_data is not None else 0,
                "urgent": len(backlog_data[backlog_data['PRIORITE'] == 'URGENT']) if backlog_data is not None else 0,
                "prioritaire": len(backlog_data[backlog_data['PRIORITE'] == 'PRIORITAIRE']) if backlog_data is not None else 0
            },
            "personnel_stats": {
                "total_actifs": len(personnel_data) if personnel_data is not None else 0,
                "qualifications_uniques": personnel_data['QUALIFICATION'].nunique() if personnel_data is not None else 0
            }
        }

        return {
            "success": True,
            "summary": summary,
            "period": {
                "date_debut": date_debut,
                "date_fin": date_fin
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching summary stats: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
