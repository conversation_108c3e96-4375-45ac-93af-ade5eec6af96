# Documentation Technique - Application Autonome de Suivi de Production

## Vue d'ensemble des Modifications

Cette documentation détaille les modifications apportées pour transformer l'application de suivi de production en une version entièrement autonome, basée uniquement sur la base de données Excalibur ERP.

## Changements Majeurs

### 1. Suppression des Dépendances Excel

#### Avant

- Dépendance aux fichiers `calcul charge modifié.xlsx` et `suivie OF.xlsx`
- Méthode `load_excel_data()` pour charger les fichiers
- Calculs post-traitement en Python avec Pandas

#### Après

- **Suppression complète** des références aux fichiers Excel
- **Calculs SQL natifs** intégrés directement dans les requêtes
- **Données en temps réel** depuis la base de données

### 2. Requêtes SQL Natives Complètes

#### Nouvelle Requête Principale (`get_comprehensive_of_data`)

```sql
SELECT
    OF_DA.NUMERO_OFDA,
    OF_DA.PRODUIT,
    OF_DA.STATUT,
    OF_DA.LANCEMENT_AU_PLUS_TARD,
    OF_DA.QUANTITE_DEMANDEE,
    OF_DA.CUMUL_ENTREES,
    OF_DA.DUREE_PREVUE,
    OF_DA.CUMUL_TEMPS_PASSES,

    -- Calculs intégrés en SQL
    DATEPART(week, OF_DA.LANCEMENT_AU_PLUS_TARD) AS SEMAINE,

    CASE
        WHEN OF_DA.QUANTITE_DEMANDEE > 0
        THEN CAST(OF_DA.CUMUL_ENTREES AS FLOAT) / CAST(OF_DA.QUANTITE_DEMANDEE AS FLOAT)
        ELSE 0
    END AS Avancement_PROD,

    CASE
        WHEN OF_DA.DUREE_PREVUE > 0
        THEN CAST(OF_DA.CUMUL_TEMPS_PASSES AS FLOAT) / CAST(OF_DA.DUREE_PREVUE AS FLOAT)
        ELSE 0
    END AS Avancement_temps,

    CASE
        WHEN OF_DA.DUREE_PREVUE > 0 AND OF_DA.CUMUL_TEMPS_PASSES > OF_DA.DUREE_PREVUE
        THEN 1
        ELSE 0
    END AS Alerte_temps,

    -- Jointure avec historique pour temps unitaires
    COALESCE(hist_avg.TEMPS_UNITAIRE_MOYEN, 0) AS TEMPS_UNITAIRE_HISTORIQUE

FROM gpao.OF_DA OF_DA
LEFT JOIN (
    SELECT
        HISTO.PRODUIT,
        HISTO.CATEGORIE,
        AVG(CASE
            WHEN HISTO.CUMUL_ENTREES > 0
            THEN CAST(HISTO.CUMUL_TEMPS_PASSES AS FLOAT) / CAST(HISTO.CUMUL_ENTREES AS FLOAT)
            ELSE 0
        END) AS TEMPS_UNITAIRE_MOYEN
    FROM gpao.HISTO_OF_DA HISTO
    WHERE HISTO.CUMUL_ENTREES > 0 AND HISTO.CUMUL_TEMPS_PASSES > 0
    GROUP BY HISTO.PRODUIT, HISTO.CATEGORIE
) hist_avg ON OF_DA.PRODUIT = hist_avg.PRODUIT AND OF_DA.CATEGORIE = hist_avg.CATEGORIE
```

### 3. Nouvelles Fonctionnalités Ajoutées

#### A. Analyse de Charge de Travail (`get_charge_travail_data`)

- **Calcul dynamique** des ressources par secteur
- **Taux de charge** en temps réel
- **Répartition** par qualification du personnel

#### B. Gestion du Backlog (`get_backlog_data`)

- **Identification automatique** des OF en retard
- **Système de priorités** (Urgent/Prioritaire/Normal)
- **Calcul du temps restant** estimé

#### C. Données Personnel (`get_personnel_data`)

- **Liste du personnel actif** avec qualifications
- **Coefficients d'efficacité** par type de qualification
- **Analyse des compétences** disponibles

### 4. Architecture Modifiée

#### Classe `ExcaliburDataAnalyzer` - Nouvelle Structure

```python
class ExcaliburDataAnalyzer:
    def __init__(self):
        # Paramètres de connexion uniquement

    def execute_query(self, query, params=None):
        # Méthode générique d'exécution SQL

    def get_comprehensive_of_data(self, date_debut, date_fin, statut_filter):
        # Requête principale avec tous les calculs

    def get_charge_travail_data(self, date_debut, date_fin):
        # Analyse de charge par secteur

    def get_backlog_data(self, date_debut, date_fin):
        # Gestion du backlog et priorités

    def get_personnel_data(self):
        # Données du personnel actif

    def get_dashboard_metrics(self, date_debut, date_fin):
        # Métriques complètes pour le tableau de bord
```

### 5. Interface Streamlit Enrichie

#### Nouveaux Onglets Conditionnels

- **⚙️ Charge Travail** : Analyse des ressources par secteur
- **📋 Backlog** : Gestion des OF en attente avec priorités
- **👥 Personnel** : Analyse des compétences et qualifications

#### Fonctionnalités Avancées

- **Filtrage dynamique** par secteur, priorité, client
- **Visualisations interactives** avec Plotly
- **Métriques en temps réel** depuis la base de données
- **Export des données** filtrées

### 6. Formules SQL Équivalentes aux Calculs Excel

#### Formules Implémentées

| Calcul Excel Original                  | Équivalent SQL                                                                |
| -------------------------------------- | ----------------------------------------------------------------------------- |
| `NO.SEMAINE(LANCEMENT_AU_PLUS_TARD)`   | `DATEPART(week, OF_DA.LANCEMENT_AU_PLUS_TARD)`                                |
| `CUMUL_ENTREES/QUANTITE_DEMANDEE`      | `CAST(OF_DA.CUMUL_ENTREES AS FLOAT) / CAST(OF_DA.QUANTITE_DEMANDEE AS FLOAT)` |
| `CUMUL_TEMPS_PASSES/DUREE_PREVUE`      | `CAST(OF_DA.CUMUL_TEMPS_PASSES AS FLOAT) / CAST(OF_DA.DUREE_PREVUE AS FLOAT)` |
| `SI(Avancement_temps>1;"ALERTE";"OK")` | `CASE WHEN CUMUL_TEMPS_PASSES > DUREE_PREVUE THEN 1 ELSE 0 END`               |

### 7. Tables de Base de Données Utilisées

#### Tables Principales

- **`gpao.OF_DA`** : Ordres de fabrication principaux
- **`gpao.HISTO_OF_DA`** : Historique pour calculs de temps unitaires
- **`gpao.SALARIES`** : Personnel actif et qualifications

#### Jointures et Relations

- **Historique** : Calcul des temps unitaires moyens par produit/catégorie
- **Personnel** : Répartition par secteur basée sur les qualifications
- **Charge** : Calcul dynamique des taux de charge par secteur

### 8. Avantages de la Version Autonome

#### Performance

- **Calculs côté serveur** : Plus rapides que les traitements Python
- **Données en temps réel** : Pas de fichiers intermédiaires
- **Moins de transferts** : Seules les données nécessaires sont récupérées

#### Maintenance

- **Pas de fichiers Excel** à maintenir
- **Calculs centralisés** dans la base de données
- **Cohérence des données** garantie

#### Évolutivité

- **Ajout facile** de nouvelles métriques via SQL
- **Intégration native** avec d'autres modules ERP
- **Historisation automatique** des données

### 9. Tests et Validation

#### Tests Automatisés

- **Connexion base de données** : Validation des paramètres
- **Requêtes SQL** : Test de toutes les fonctions d'analyse
- **Interface Streamlit** : Validation des composants UI
- **Données cohérentes** : Vérification des calculs

#### Résultats de Test

```
✅ Connexion à la base de données réussie
✅ Données OF récupérées: 36 enregistrements
✅ Données de charge récupérées: 4 secteurs
✅ Données de personnel récupérées: 108 employés
```

### 10. Migration et Déploiement

#### Étapes de Migration

1. **Sauvegarde** des fichiers Excel existants (référence)
2. **Test** de la nouvelle version en parallèle
3. **Validation** des calculs par rapport aux résultats Excel
4. **Déploiement** de la version autonome
5. **Formation** des utilisateurs aux nouvelles fonctionnalités

#### Configuration Requise

- **Accès réseau** au serveur Excalibur (192.168.1.200:2638)
- **Droits de lecture** sur les tables gpao.\*
- **Python 3.8+** avec les dépendances listées dans requirements.txt
- **Driver ODBC SQL Anywhere** installé sur le système

### 11. Maintenance Future

#### Ajout de Nouvelles Métriques

1. Créer la requête SQL dans `ExcaliburDataAnalyzer`
2. Ajouter l'affichage dans l'interface Streamlit
3. Mettre à jour les tests automatisés

#### Optimisation des Performances

- **Index** sur les colonnes de filtrage fréquent
- **Vues matérialisées** pour les calculs complexes
- **Cache** des requêtes lentes

Cette version autonome offre une base solide pour l'évolution future de l'application de suivi de production, avec une architecture moderne et maintenable.
